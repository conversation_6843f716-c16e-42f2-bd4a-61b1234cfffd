"use client"

import { motion } from "framer-motion"
import { Icons } from "@/components/icons"
import { cn } from "@/lib/utils"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showBackButton?: boolean
  backHref?: string
  className?: string
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showBackButton = false,
  backHref = "/",
  className,
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-200/20 to-transparent dark:via-slate-700/20" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-200/20 to-transparent dark:via-slate-700/20" />
      </div>
      
      <div className="relative flex min-h-screen">
        {/* Left Side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center p-12 relative overflow-hidden">
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-pink-600/10" />
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative z-10 text-center space-y-8"
          >
            {/* Logo */}
            <div className="flex justify-center">
              <Icons.logoFull className="h-12 text-foreground" />
            </div>
            
            {/* Hero Text */}
            <div className="space-y-4 max-w-md">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
                Welcome to the intelligence era of investing
              </h1>
              <p className="text-lg text-muted-foreground leading-relaxed">
                TractionX empowers investors with AI-driven insights, automated deal flow, and intelligent thesis matching.
              </p>
            </div>
            
            {/* Features */}
            <div className="space-y-3 text-left">
              {[
                "AI-powered deal analysis",
                "Automated thesis matching", 
                "Real-time market intelligence"
              ].map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full" />
                  <span className="text-sm text-muted-foreground">{feature}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Right Side - Auth Form */}
        <div className="flex-1 lg:w-1/2 flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className={cn("w-full max-w-md space-y-8", className)}
          >
            {/* Mobile Logo */}
            <div className="lg:hidden flex justify-center">
              <Icons.logoFull className="h-8 text-foreground" />
            </div>
            
            {/* Header */}
            <div className="text-center space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">{title}</h2>
              <p className="text-muted-foreground">{subtitle}</p>
            </div>

            {/* Form Content */}
            {children}
            
            {/* Footer */}
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                You're accessing TractionX Beta. 
                <br />
                Built with ❤️ for the future of investing.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
